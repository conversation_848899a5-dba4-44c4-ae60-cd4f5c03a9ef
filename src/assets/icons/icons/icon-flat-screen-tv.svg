<svg width="220" height="160" viewBox="0 0 220 160" fill="none" xmlns="http://www.w3.org/2000/svg"
    stroke="#011589" stroke-width="3">
    <!-- TV screen -->
    <rect x="10" y="20" width="120" height="70" />

    <!-- Play icon inside TV -->
    <polygon points="60,45 75,55 60,65" fill="none" stroke="#011589" stroke-width="3" />

    <!-- TV stand with two legs -->
    <line x1="50" y1="90" x2="50" y2="110" />
    <line x1="90" y1="90" x2="90" y2="110" />
    <line x1="40" y1="110" x2="100" y2="110" />

    <!-- Speaker box (starts at mid height of TV) -->
    <rect x="130" y="55" width="50" height="70" />

    <!-- Speaker small lines -->
    <line x1="140" y1="65" x2="155" y2="65" />
    <line x1="140" y1="75" x2="150" y2="75" />
    <line x1="140" y1="85" x2="150" y2="85" />

    <!-- Speaker circle -->
    <circle cx="155" cy="110" r="10" />
</svg>
  