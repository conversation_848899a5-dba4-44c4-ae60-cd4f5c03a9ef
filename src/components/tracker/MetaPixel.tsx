import Script from 'next/script';

const MetaPixel = ({ pixelIds = [] }: { pixelIds: string[] }) => {
  if (!pixelIds?.length) return null;
  return (
    <>
      {/* Meta Pixel Script using Next.js Script component */}
      <Script
        id='meta-pixel'
        src='https://connect.facebook.net/en_US/fbevents.js'
        strategy='afterInteractive'
        onLoad={() => {
          // Initialize Meta Pixel after script loads
          if (typeof window !== 'undefined' && window.fbq) {
            pixelIds.map((pixelId) => {
              return window.fbq('init', pixelId);
            });
            window.fbq('track', 'PageView');
          }
        }}
      />

      <Script
        id='meta-pixel-init'
        strategy='afterInteractive'
        dangerouslySetInnerHTML={{
          __html: `
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];}(window,document,'script');
            ${pixelIds
              .map((pixelId) => `fbq('init', '${pixelId}');`)
              .join('\n')}
            fbq('track', 'PageView');
          `,
        }}
      />

      {/* Fallback noscript image */}
      {pixelIds.map((pixelId) => (
        <noscript key={pixelId}>
          <img
            height='1'
            width='1'
            style={{ display: 'none' }}
            src={`https://www.facebook.com/tr?id=${pixelId}&ev=PageView&noscript=1`}
            alt=''
          />
        </noscript>
      ))}
    </>
  );
};

export default MetaPixel;
