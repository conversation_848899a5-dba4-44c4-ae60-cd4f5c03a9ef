import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  FormControl,
  Select,
  MenuItem,
  Button,
  Divider,
  Stack,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
// import { useRouter } from 'next/navigation';
import { SLIDES } from '../HeroSection/constant';
import KeyboardBackspaceIcon from '@mui/icons-material/KeyboardBackspace';
import { calculateCheckInDatetime } from './utils';
import DatetimeSelections from './DatetimeSelections';
import { FilterData } from './types';

interface FilterRoomsSectionProps {
  onApplyFilter?: (filters: FilterData) => void;
}

/**
 * FilterRoomsSection Component
 *
 * This component handles room filtering with URL parameter synchronization.
 *
 * URL Parameters handled:
 * - lotId: The lot/outlet identifier
 * - date: Check-in date (ISO string format, URL encoded)
 * - checkInTime: Check-in time
 * - duration: Stay duration in hours
 *
 * Features:
 * - Reads initial filter values from URL parameters on mount
 * - Updates URL parameters when user clicks "UPDATE" button
 * - Syncs filter state with browser back/forward navigation
 * - API calls (onApplyFilter) are triggered:
 *   1. Automatically on first load if valid URL parameters exist (for shared links)
 *   2. When user explicitly clicks "UPDATE" button
 *   3. When navigating to a URL with valid parameters via browser navigation
 *
 * Example URL:
 * /new-booking?lotId=123&date=2024-01-15T00%3A00%3A00.000Z&checkInTime=14:00&duration=6
 */

const FilterRoomsSection: React.FC<FilterRoomsSectionProps> = ({
  onApplyFilter: onApplyFilter,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  // const router = useRouter();

  // State to track if user has scrolled down
  const [isScrolled, setIsScrolled] = useState(false);

  // Parse URL parameters and set initial state
  const getInitialFilters = (): FilterData => {
    if (typeof window === 'undefined') {
      return {
        lotId: '',
        outlet: '',
        date: null,
        checkInTime: '',
        stayDuration: 0,
      };
    }

    const urlParams = new URLSearchParams(window.location.search);
    const lotId = urlParams.get('lotId') || '';
    const dateParam = urlParams.get('date');
    const checkInTime = urlParams.get('checkInTime') || '';
    const duration = urlParams.get('duration');

    // Parse date from URL parameter
    let parsedDate = new Date();
    if (dateParam) {
      try {
        // Decode the URL-encoded date string
        const decodedDate = decodeURIComponent(dateParam);
        parsedDate = new Date(decodedDate);

        // Check if the parsed date is valid
        if (isNaN(parsedDate.getTime())) {
          parsedDate = new Date();
        }
      } catch (error) {
        console.warn('Failed to parse date from URL:', dateParam);
        parsedDate = new Date();
      }
    }

    // Parse duration from URL parameter
    let parsedDuration = 0;
    if (duration) {
      try {
        parsedDuration = parseInt(duration, 10);
        if (isNaN(parsedDuration)) {
          parsedDuration = 0;
        }
      } catch (error) {
        console.warn('Failed to parse duration from URL:', duration);
        parsedDuration = 0;
      }
    }

    // Find the outlet name based on lotId
    let outletName = '';
    if (lotId) {
      const slide = SLIDES.find((slide) => slide.lotId.toString() === lotId);
      if (slide) {
        outletName = `${slide.terminal} - ${slide.title}`;
      }
    }

    // Create a proper checkInDatetime by combining date and checkInTime
    let checkInDatetime = 0;
    if (parsedDate && checkInTime) {
      checkInDatetime = calculateCheckInDatetime(parsedDate, checkInTime);
    }

    return {
      lotId,
      outlet: outletName,
      date: parsedDate,
      checkInTime,
      stayDuration: parsedDuration,
      checkInDatetime,
    };
  };

  const [filters, setFilters] = useState<FilterData>(getInitialFilters);

  // Utility function to update URL parameters
  const updateUrlParams = (filterData: FilterData) => {
    if (typeof window === 'undefined') return;

    const urlParams = new URLSearchParams(window.location.search);

    // Update or remove parameters based on filter data
    if (filterData.lotId) {
      urlParams.set('lotId', filterData.lotId);
    } else {
      urlParams.delete('lotId');
    }

    if (filterData.date) {
      // Encode the date as ISO string
      urlParams.set('date', encodeURIComponent(filterData.date.toISOString()));
    } else {
      urlParams.delete('date');
    }

    if (filterData.checkInTime) {
      urlParams.set('checkInTime', filterData.checkInTime);
    } else {
      urlParams.delete('checkInTime');
    }

    if (filterData.stayDuration && filterData.stayDuration > 0) {
      urlParams.set('duration', filterData.stayDuration.toString());
    } else {
      urlParams.delete('duration');
    }

    // Use router.replace to update URL without adding to history
    const newUrl = `${window.location.pathname}?${urlParams.toString()}`;
    // router.replace(newUrl);
    window.history.replaceState(null, '', newUrl);
  };

  // Initialize filters from URL parameters on mount
  useEffect(() => {
    const newFilters = getInitialFilters();
    setFilters(newFilters);

    // If valid URL parameters exist on first load, call the API to show results
    // This ensures that shared URLs with parameters work immediately
    if (
      onApplyFilter &&
      newFilters.lotId &&
      newFilters.checkInTime &&
      (newFilters.stayDuration || 0) > 0
    ) {
      // Ensure checkInDatetime is properly calculated
      const updatedCheckInDatetime = calculateCheckInDatetime(
        newFilters.date || null,
        newFilters.checkInTime || ''
      );

      const updatedFilters = {
        ...newFilters,
        checkInDatetime: updatedCheckInDatetime,
      };

      onApplyFilter(updatedFilters);
    }
  }, [onApplyFilter]); // Include onApplyFilter in dependency array

  // Listen for URL changes (e.g., browser back/forward)
  useEffect(() => {
    const handlePopState = () => {
      const newFilters = getInitialFilters();
      setFilters(newFilters);

      // If user navigates to a URL with valid parameters, apply filters
      if (
        onApplyFilter &&
        newFilters.lotId &&
        newFilters.checkInTime &&
        (newFilters.stayDuration || 0) > 0
      ) {
        const updatedCheckInDatetime = calculateCheckInDatetime(
          newFilters.date || null,
          newFilters.checkInTime || ''
        );

        const updatedFilters = {
          ...newFilters,
          checkInDatetime: updatedCheckInDatetime,
        };

        onApplyFilter(updatedFilters);
      }
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [onApplyFilter]);

  // Listen for scroll events to adjust component height
  useEffect(() => {
    if (isMobile) {
      setIsScrolled(false);
      return;
    }

    const handleScroll = () => {
      const scrollTop =
        window.pageYOffset || document.documentElement.scrollTop;
      // Consider scrolled if user has scrolled more than 100px from top
      setIsScrolled(scrollTop > 100);
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isMobile]);

  const handleOutletChange = (value: string) => {
    setFilters((prev) => ({ ...prev, lotId: value }));
  };

  const handleDateChange = (date: Date | null) => {
    setFilters((prev) => ({ ...prev, date }));
  };

  const handleCheckInTimeChange = (time: string) => {
    setFilters((prev) => ({ ...prev, checkInTime: time }));
  };

  const handleStayDurationChange = (duration: number) => {
    setFilters((prev) => ({ ...prev, stayDuration: duration }));
  };

  const handleUpdate = () => {
    // Recalculate checkInDatetime before applying filters
    const updatedCheckInDatetime = calculateCheckInDatetime(
      filters.date || null,
      filters.checkInTime || ''
    );

    const updatedFilters = {
      ...filters,
      checkInDatetime: updatedCheckInDatetime,
    };

    // Update URL parameters first
    updateUrlParams(updatedFilters);

    // Then apply the filters
    if (onApplyFilter) {
      onApplyFilter(updatedFilters);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box
        sx={{
          p: isScrolled ? { xs: 1, md: 1.5 } : { xs: 1.5, md: 2 },
          bgcolor: 'white',
          // borderRadius: 2,
          borderBottomLeftRadius: 2,
          borderBottomRightRadius: 2,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          transition: 'padding 0.3s ease-in-out',
        }}
      >
        <Stack
          direction={{ xs: 'column', md: 'row' }}
          spacing={isScrolled ? { xs: 1.5, md: 2 } : { xs: 2, md: 2.5 }}
          alignItems={{ xs: 'stretch', md: 'flex-start' }}
          sx={{
            width: '100%',
            transition: 'all 0.3s ease-in-out',
          }}
        >
          {/* Outlet Selection */}
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography
              variant='caption'
              sx={{
                fontWeight: 600,
                mb: isScrolled ? 0.5 : 1,
                color: theme.palette.CtColorScheme.grey400,
                display: 'block',
                textTransform: 'uppercase',
                fontSize: '0.75rem',
                transition: 'margin 0.3s ease-in-out',
              }}
            >
              OUTLET
            </Typography>
            <FormControl fullWidth size='small'>
              <Select
                value={filters.lotId}
                onChange={(e) => handleOutletChange(e.target.value)}
                displayEmpty
                sx={{
                  '& .MuiSelect-select': {
                    py: 1.5,
                    px: 2,
                    fontSize: '0.875rem',
                    color: '#0F0E0E',
                  },
                  height: '100%',
                  maxHeight: '40px',
                  border: `1px solid #0F0E0E`,
                  borderRadius: '4px',
                  '& .MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                }}
              >
                <MenuItem value='' disabled>
                  Select outlet
                </MenuItem>
                {SLIDES.map((slide, index) => (
                  <MenuItem key={index} value={slide.lotId}>
                    {slide.terminal} - {slide.title}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            {!isScrolled && (
              <Typography
                variant='caption'
                sx={{
                  color: '#4D5CAC',
                  textDecoration: 'underline',
                  cursor: 'pointer',
                  mt: 0.5,
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '0.75rem',
                  transition: 'opacity 0.3s ease-in-out',
                }}
              >
                <KeyboardBackspaceIcon sx={{ fontSize: '14px' }} /> Guide me on
                booking again
              </Typography>
            )}
          </Box>

          {/* Divider - Vertical on desktop, horizontal on mobile */}
          <Divider
            orientation={isMobile ? 'horizontal' : 'vertical'}
            flexItem
            sx={{
              height: isMobile ? '1px' : isScrolled ? '68px' : '92px',
              backgroundColor: theme.palette.CtColorScheme.grey100,
              mx: isMobile ? 0 : 1,
              my: isMobile ? (isScrolled ? 0.25 : 0.5) : 0,
              transition: 'height 0.3s ease-in-out, margin 0.3s ease-in-out',
            }}
          />

          <DatetimeSelections
            filters={filters}
            handleDateChange={handleDateChange}
            handleCheckInTimeChange={handleCheckInTimeChange}
            handleStayDurationChange={handleStayDurationChange}
            isScrolled={isScrolled}
          />

          {/* Update Button - Responsive positioning */}
          <Box
            sx={{
              display: 'flex',
              alignItems: { xs: 'stretch', md: 'flex-end' },
              paddingTop: {
                xs: 0,
                md: isScrolled ? '24px' : '28px',
              },
              ml: { xs: 0, md: 1.5 },
              mt: {
                xs: isScrolled ? 1 : 1.5,
                md: 0,
              },
              transition: 'padding 0.3s ease-in-out, margin 0.3s ease-in-out',
            }}
          >
            <Button
              variant='contained'
              onClick={handleUpdate}
              fullWidth={isMobile}
              sx={{
                bgcolor: theme.palette.CtColorScheme.blue800,
                color: 'white',
                py: 1.5,
                px: 3,
                fontSize: '0.875rem',
                fontWeight: 600,
                height: '100%',
                maxHeight: '40px',
                borderRadius: '4px',
                textTransform: 'uppercase',
                '&:hover': {
                  bgcolor: theme.palette.CtColorScheme.blue800,
                  opacity: 0.9,
                },
              }}
            >
              UPDATE
            </Button>
          </Box>
        </Stack>
      </Box>
    </LocalizationProvider>
  );
};

export default FilterRoomsSection;
