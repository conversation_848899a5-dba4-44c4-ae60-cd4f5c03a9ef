import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  Stack,
  Divider,
  Slide,
  Fade,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { ArrowForward } from '@mui/icons-material';
interface SelectedRoomItem {
  id: string;
  roomType: string;
  quantity: number;
  zone: string;
  duration: string;
  price: string;
}

interface SelectedRoomProps {
  selectedRooms: SelectedRoomItem[];
  onBookNow: () => void;
}

// Individual room item component
const SelectedRoomItem: React.FC<{ room: SelectedRoomItem }> = ({ room }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <Stack
      direction={isMobile ? 'column' : 'row'}
      spacing={2}
      alignItems={isMobile ? 'stretch' : 'center'}
      justifyContent='space-between'
    >
      {/* Left side - Room details */}
      <Box sx={{ flex: 1 }}>
        <Typography
          variant='body1'
          sx={{
            color: '#011589', // Darker blue text
            fontWeight: 500,
            fontSize: '16px',
          }}
        >
          {room.quantity} x {room.roomType} @ {room.zone} for {room.duration}
        </Typography>
      </Box>
      {/* Right side - Price and button */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-end',
          gap: 1,
        }}
      >
        <Typography
          variant='h4'
          sx={{
            fontWeight: 700,
            color: '#011589', // Dark blue price
            lineHeight: 1,
            fontSize: '32px',
          }}
        >
          {room.price}
        </Typography>
        <Typography
          variant='caption'
          sx={{
            color: '#1A1A1A',
            fontSize: '14px',
          }}
        >
          Price before discount and tax
        </Typography>
      </Box>
    </Stack>
  );
};

const SelectedRoom: React.FC<SelectedRoomProps> = ({
  selectedRooms,
  onBookNow,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Calculate total price
  const countTotal = selectedRooms.reduce<{
    totalPrice: number;
    totalQuantity: number;
  }>(
    (obj, room: SelectedRoomItem) => {
      // Extract numeric value from price string (e.g., "RM135" -> 135)
      const priceValue = Number(room.price);

      obj.totalQuantity += room.quantity;

      obj.totalPrice += priceValue * room.quantity;

      return obj;
    },
    { totalPrice: 0, totalQuantity: 0 }
  );

  const title =
    selectedRooms.length > 1
      ? `${countTotal.totalQuantity} Rooms Selected`
      : selectedRooms?.[0]
      ? `${countTotal.totalQuantity} x ${selectedRooms[0].roomType}`
      : '';

  return (
    <Slide
      direction='up'
      in={selectedRooms?.length > 0}
      mountOnEnter
      unmountOnExit
    >
      <Fade in={selectedRooms?.length > 0} timeout={300}>
        <Box
          sx={{
            width: '100%',
            maxWidth: '1144px',
            margin: '0 auto',
            position: 'fixed',
            bottom: '22px',
            left: 0,
            right: 0,
            zIndex: 100,
            padding: '0 16px'
          }}
        >
          <Card
            sx={{
              borderRadius: '8px',
              backgroundColor: '#EEFCFA',
              border: '1px solid #EEFCFA',
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Stack
                direction={isMobile ? 'column' : 'row'}
                spacing={2}
                alignItems={isMobile ? 'stretch' : 'center'}
                justifyContent='space-between'
                sx={{
                  ...(isMobile
                    ? {}
                    : {
                        display: 'grid',
                        gridTemplateColumns: '1fr 10px 200px',
                      }),
                }}
              >
                <Box
                  sx={{
                    mb: isMobile ? 2 : 3,
                    display: 'flex',
                    justifyContent: 'space-between',
                    flexDirection: 'row',
                    gap: '6px',
                  }}
                >
                  <Box>
                    <Typography
                      variant='h6'
                      sx={{
                        fontWeight: 700,
                        color: '#1A1A1A',
                        fontSize: '16px',
                        marginBottom: { xs: '8px', md: '6px' },
                      }}
                    >
                      Selected Room
                    </Typography>

                    <Box sx={{ mb: 0 }}>
                      <Typography
                        variant='body1'
                        sx={{
                          color: '#011589', // Darker blue text
                          fontWeight: 600,
                          fontSize: '18px',
                        }}
                      >
                        {title}
                      </Typography>
                    </Box>
                  </Box>

                  {/* Desktop price layout */}
                  <Stack
                    direction='row'
                    spacing={2}
                    alignItems='center'
                    justifyContent='space-between'
                  >
                    {/* Room details already shown above */}
                    <Box sx={{ flex: 1 }} />
                    {/* Total Price */}
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'flex-end',
                        gap: 1,
                      }}
                    >
                      <Typography
                        variant='h4'
                        sx={{
                          fontWeight: 700,
                          color: '#011589', // Dark blue price
                          lineHeight: 1,
                          fontSize: { xs: '24px', md: '32px' },
                        }}
                      >
                        RM{countTotal.totalPrice}
                      </Typography>
                      <Typography
                        variant='caption'
                        sx={{
                          color: '#1A1A1A',
                          fontSize: { xs: '12px', md: '14px' },
                        }}
                      >
                        Price before discount and tax
                      </Typography>
                    </Box>
                  </Stack>
                </Box>

                {/* Divider - only show on desktop */}
                {!isMobile && (
                  <Divider
                    orientation='vertical'
                    flexItem
                    sx={{
                      borderColor: '#BDBDBD',
                      opacity: 0.6,
                    }}
                  />
                )}

                {/* Button section */}
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: isMobile ? 'stretch' : 'flex-end',
                    gap: 1,
                  }}
                >
                  <Button
                    variant='contained'
                    onClick={onBookNow}
                    endIcon={<ArrowForward />}
                    sx={{
                      backgroundColor: '#011589',
                      color: 'white',
                      borderRadius: '4px',
                      px: 3,
                      py: 1.5,
                      fontWeight: 600,
                      textTransform: 'none',
                      fontSize: '16px',
                      width: isMobile ? '100%' : 'auto',
                      '&:hover': {
                        backgroundColor: '#011589',
                      },
                      mt: 1,
                    }}
                  >
                    BOOK NOW
                  </Button>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Box>
      </Fade>
    </Slide>
  );
};

export default SelectedRoom;
