'use client';

import React, { useState } from 'react';
import {
  Box,
  Divider,
  Grid,
  SxProps,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { ExpandMore, ExpandLess } from '@mui/icons-material';
import Image from 'next/image';

import { FACILITIES } from '@/constant/outlet';

interface FacilityItem {
  name: string;
  icon: any;
}

const facilities: FacilityItem[] = [
  FACILITIES.wifi,
  FACILITIES.airConditioning,
  FACILITIES.receptionDesk,
  FACILITIES.loungeLibrary,
  FACILITIES.femaleZoneAvailable,
  FACILITIES.luggageStorageLocker,
  FACILITIES.shoeLocker,
  FACILITIES.sleepingEyeMask,
  FACILITIES.earplug,
  FACILITIES.inHouseSlippers,
  FACILITIES.dentalKit,
  FACILITIES.sharedBathroom,
  FACILITIES.rainShower,
  FACILITIES.toiletries,
  FACILITIES.showerTowel,
  FACILITIES.bidetHairdryer,
  FACILITIES.freeFlowTeaCoffee,
  FACILITIES.bottledWater,
  FACILITIES.safetyDepositBox,
  FACILITIES.socketNearBed,
];

const FacilitiesAndAmenities = ({ sx }: { sx?: SxProps }) => {
  const isMobile = useMediaQuery('(max-width:768px)');
  const [showFullText, setShowFullText] = useState(false);
  const [showAllFacilities, setShowAllFacilities] = useState(false);

  const fullText =
    "Situated at the Gateway@klia2 Mall, linked to KLIA Terminal 2's Arrival Hall, CapsuleTransit Landside outlet offers room for 1 to 3 guests, available to book for a minimum of 3 hours. Each room includes public bathrooms and locker services. The hotel is located in the public area (Landside) of Kuala Lumpur International Airport Terminal 2. Please pass the passport control and enter Malaysia if you wish to stay at our hotel during a layover between international flights.";

  const truncatedText =
    "Situated at the Gateway@klia2 Mall, linked to KLIA Terminal 2's Arrival Hall, CapsuleTransit Landside outlet offers room for 1 to 3 guests, available to book for a minimum of 3 hours.";

  // Get facilities to display based on mobile state
  const getFacilitiesToShow = () => {
    if (!isMobile) {
      return facilities;
    }
    return showAllFacilities ? facilities : facilities.slice(0, 6);
  };

  return (
    <Box
      sx={{
        backgroundColor: '#f8f6ff', // Light purple background
        px: { xs: 2, md: 8 },
        py: { xs: 8, md: 8 },
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        ...(sx || {}),
      }}
    >
      <Box
        sx={{
          maxWidth: '1376px',
          margin: '0 auto',
          width: '100%',
        }}
      >
        {/* Main Heading */}
        <Typography
          variant='h2'
          sx={{
            fontWeight: 700,
            color: '#1a1a1a',
            fontSize: { xs: '2rem', md: '3rem' },
            lineHeight: 1.2,
            marginBottom: 3,
            textAlign: { xs: 'left', md: 'left' },
          }}
        >
          For lone travellers, small groups and families;
          <Box component='br' sx={{ display: { xs: 'none', md: 'block' } }} />
          <Box component='span' sx={{ display: { xs: 'initial', md: 'none' } }}>
            &nbsp;
          </Box>
          less hassle, more time to rest.
        </Typography>

        {/* Description Paragraph */}
        <Box>
          <Typography
            variant='body1'
            sx={{
              color: '#333',
              fontSize: { xs: '16px' },
              lineHeight: 1.6,
              marginBottom: { xs: '6px' },
              textAlign: { xs: 'left', md: 'left' },
              // maxWidth: '800px',
            }}
          >
            {/* Show full text on desktop, truncated on mobile by default */}
            <Box
              component='span'
              sx={{ display: { xs: 'none', md: 'inline' } }}
            >
              {fullText}
            </Box>
            <Box
              component='span'
              sx={{ display: { xs: 'inline', md: 'none' } }}
            >
              {showFullText ? fullText : truncatedText}
            </Box>
          </Typography>

          {/* Show More/Less Button - Only visible on mobile */}
          <Box
            onClick={() => setShowFullText(!showFullText)}
            sx={{
              display: { xs: 'flex', md: 'none' },
              alignItems: 'center',
              justifyContent: 'flex-start',
              marginBottom: '10px',
              gap: 1,
              width: 'fit-content',
              fontWeight: 'normal',
              color: '#011589',
              fontSize: '14px',
              textTransform: 'none',
              minWidth: 'auto',
              transition: 'all 0.2s ease-in-out',
              textDecoration: 'underline',
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: 'transparent',
                textDecoration: 'underline',
              },
            }}
          >
            {showFullText ? 'Read Less' : 'Read More'}
          </Box>
        </Box>

        {/* Facilities and Amenities Section */}
        <Box sx={{ marginTop: 8 }}>
          <Typography
            variant='h3'
            sx={{
              fontWeight: 700,
              color: '#1a1a1a',
              fontSize: { xs: '1.5rem', md: '2rem' },
              marginBottom: 4,
              textAlign: { xs: 'center', md: 'left' },
            }}
          >
            Facilities and Amenities
          </Typography>

          {/* Facilities Grid */}
          <Grid container spacing={1.5}>
            {getFacilitiesToShow().map((facility, index) => (
              <Grid item xs={6} sm={6} md={4} lg={2.4} key={index}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: { xs: '4px', md: '8px' },
                    padding: 0,
                  }}
                >
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexShrink: 0,
                      color: '#011589',
                    }}
                  >
                    <Image
                      src={facility.icon}
                      alt={facility.name}
                      width={24}
                      height={24}
                      style={{
                        filter:
                          'brightness(0) saturate(100%) invert(6%) sepia(87%) saturate(4451%) hue-rotate(240deg) brightness(98%) contrast(101%)',
                      }}
                    />
                  </Box>
                  <Typography
                    variant='body2'
                    sx={{
                      fontWeight: '500 !important',
                      color: '#3B3F47',
                      fontSize: { xs: '14px', md: '15px' },
                      lineHeight: 1.2,
                    }}
                  >
                    {facility.name}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>

          {/* Mobile Show More/Less Section */}
          <Box sx={{ display: { xs: 'block', md: 'none' } }}>
            <Divider
              sx={{
                marginTop: 1,
                marginBottom: 1,
                borderColor: '#E0E0E0',
              }}
            />
            <Box
              onClick={() => setShowAllFacilities(!showAllFacilities)}
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 1,
                cursor: 'pointer',
                padding: 1,
                transition: 'all 0.2s ease-in-out',
              }}
            >
              <Typography
                sx={{
                  fontWeight: 500,
                  color: '#011589',
                  fontSize: '14px',
                  textDecoration: 'underline',
                  textTransform: 'none',
                }}
              >
                {showAllFacilities ? 'Close' : 'View All'}
              </Typography>
              {showAllFacilities ? (
                <ExpandLess sx={{ color: '#011589', fontSize: 20 }} />
              ) : (
                <ExpandMore sx={{ color: '#011589', fontSize: 20 }} />
              )}
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default FacilitiesAndAmenities;
