'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';

import GalleryImages from './GalleryImages';
import { RoomImagesApiResponse } from '@/actions/getRoomImages/types';
import { Outlet } from '@/actions/getOutlets/types';

const Gallery = ({
  outlet,
  roomImageData,
}: {
  outlet?: Outlet;
  roomImageData: RoomImagesApiResponse;
}) => {
  return (
    <Box sx={{ py: 8, px: { xs: 2, md: 4 } }}>
      <Typography
        variant='h2'
        component='h2'
        sx={{
          textAlign: 'center',
          mb: 6,
          fontWeight: 700,
          color: 'primary.main',
          fontSize: { xs: '2rem', md: '3rem' },
        }}
      >
        Experience Our Facilities
      </Typography>

      <GalleryImages outlet={outlet as any} roomImageData={roomImageData} />
    </Box>
  );
};

export default Gallery;
