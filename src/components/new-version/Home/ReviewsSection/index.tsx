'use client';

import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import { Box, Typography, IconButton } from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { ReviewData } from '@/actions/getReviews/types';
import ReviewCard from '../../ReviewCard';
import { ReviewCardSkeleton } from '../../ReviewCard/components/ReviewCardSkeleton';

// Clean CSS for mobile-first swiper
const swiperStyles = `
  .reviews-swiper {
    width: 100% !important;
    overflow: visible !important;
  }

  .reviews-swiper .swiper-wrapper {
    display: flex;
    align-items: stretch;
  }

  .reviews-swiper .swiper-slide {
    height: auto !important;
    display: flex !important;
    flex-shrink: 0 !important;
  }

  /* Mobile: Centered single cards */
  @media (max-width: 767px) {
    .reviews-swiper .swiper-slide {
      width: calc(100vw - 80px) !important;
      max-width: 350px !important;
      justify-content: center !important;
    }
  }

  /* Desktop: Auto width for multiple cards */
  @media (min-width: 768px) {
    .reviews-swiper .swiper-slide {
      width: 400px !important;
      max-width: 400px !important;
    }
  }
`;

interface ReviewsSectionProps {
  reviewsData: ReviewData[];
}

const ReviewsSection = ({ reviewsData }: ReviewsSectionProps) => {
  const isLoading = false;


  return (
    <>
      <style>{swiperStyles}</style>
      <Box
        sx={{
          width: '100%',
          minHeight: 500,
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
        }}
      >
        {/* Left: Heading and Nav (Desktop) / Top (Mobile) */}
        <Box
          sx={{
            width: { xs: '100%', md: '40%' },
            bgcolor: 'primary.main',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            px: 4,
            py: 8,
            color: 'white',
            position: 'relative',
            zIndex: 100
          }}
        >
          <Box>
            <Typography
              variant='overline'
              sx={{
                fontWeight: 600,
                mb: 1,
                letterSpacing: '0.1em',
                display: 'block',
                fontSize: '15px',
                opacity: isLoading ? 0.7 : 1,
                transition: 'opacity 0.6s ease-in-out',
              }}
            >
              OUR REVIEWS AND RECOGNITION
            </Typography>
            <Typography
              variant='h2'
              sx={{
                fontSize: { xs: '28px', md: '32px' },
                fontWeight: 600,
                mb: 2,
                lineHeight: 1.2,
                opacity: isLoading ? 0.7 : 1,
                transition: 'opacity 0.6s ease-in-out',
              }}
            >
              Rest at the KLIA airport with a peace of mind
            </Typography>
            <Typography
              sx={{
                mb: 4,
                opacity: isLoading ? 0.6 : 0.9,
                fontSize: { xs: '16px', md: '16px' },
                fontWeight: 400,
                transition: 'opacity 0.6s ease-in-out',
              }}
            >
              Trust not just our words, but the travellers from around the world
              who stayed with us.
            </Typography>
          </Box>
          {/* Desktop navigation buttons */}
          <Box sx={{ display: { xs: 'none', md: 'flex' }, gap: 1.5, mt: 2 }}>
            <IconButton
              className='custom-swiper-prev'
              disabled={isLoading}
              sx={{
                width: 48,
                height: 48,
                borderRadius: 1.5,
                bgcolor: 'white',
                color: '#011589',
                fontSize: '1.25rem',
                '&:hover': {
                  bgcolor: 'white',
                },
                '&.Mui-disabled': {
                  backgroundColor: 'rgba(255, 255, 255, 0.5)',
                  color: 'rgba(1, 21, 137, 0.5)',
                },
              }}
              aria-label='Previous Review'
            >
              <ChevronLeftIcon />
            </IconButton>
            <IconButton
              className='custom-swiper-next'
              disabled={isLoading}
              sx={{
                width: 48,
                height: 48,
                borderRadius: 1.5,
                bgcolor: 'white',
                color: '#011589',
                fontSize: '1.25rem',
                '&:hover': {
                  bgcolor: 'white',
                },
                '&.Mui-disabled': {
                  backgroundColor: 'rgba(255, 255, 255, 0.5)',
                  color: 'rgba(1, 21, 137, 0.5)',
                },
              }}
              aria-label='Next Review'
            >
              <ChevronRightIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Right: Swiper Carousel (Desktop) / Bottom (Mobile) */}
        <Box
          sx={{
            width: { xs: '100%', md: '60%' },
            bgcolor: '#e5e8f6',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
            minHeight: 500,
            py: 4,
          }}
        >
          <Box sx={{ width: '100%', maxWidth: '100%' }}>
            <Swiper
              modules={[Navigation]}
              slidesPerView='auto'
              spaceBetween={20}
              grabCursor={true}
              allowTouchMove={true}
              navigation={{
                prevEl: '.custom-swiper-prev',
                nextEl: '.custom-swiper-next',
              }}
              breakpoints={{
                0: {
                  slidesPerView: 1,
                  spaceBetween: 20,
                  centeredSlides: true,
                },
                768: {
                  slidesPerView: 'auto',
                  spaceBetween: 30,
                  centeredSlides: false,
                },
              }}
              style={{
                width: '100%',
                padding: '0 20px',
              }}
              className='reviews-swiper'
            >
              {isLoading
                ? // Show skeleton loading cards
                  Array.from({ length: 2 }).map((_, index) => (
                    <SwiperSlide key={`skeleton-${index}`}>
                      <ReviewCardSkeleton index={index} />
                    </SwiperSlide>
                  ))
                : // Show actual reviews with smooth fade-in
                  reviewsData.map((review, index) => (
                    <SwiperSlide key={index}>
                      <ReviewCard
                        review={review}
                        platform={review.platform}
                        platformUrl={review.platformUrl}
                        reviewPageUrl={review?.reviewPageUrl}
                      />
                    </SwiperSlide>
                  ))}
            </Swiper>

            {/* Mobile navigation buttons */}
            <Box
              sx={{
                display: { xs: 'flex', md: 'none' },
                justifyContent: 'center',
                gap: 1.5,
                mt: 4,
              }}
            >
              <IconButton
                className='custom-swiper-prev'
                disabled={isLoading}
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: 1.5,
                  bgcolor: '#011589',
                  color: 'white',
                  fontSize: '1.25rem',
                  '&:hover': {
                    bgcolor: '#011589',
                  },
                  '&.Mui-disabled': {
                    backgroundColor: 'rgba(1, 21, 137, 0.5)',
                    color: 'rgba(255, 255, 255, 0.5)',
                  },
                }}
                aria-label='Previous Review'
              >
                <ChevronLeftIcon />
              </IconButton>
              <IconButton
                className='custom-swiper-next'
                disabled={isLoading}
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: 1.5,
                  bgcolor: '#011589',
                  color: 'white',
                  fontSize: '1.25rem',
                  '&:hover': {
                    bgcolor: '#011589',
                  },
                  '&.Mui-disabled': {
                    backgroundColor: 'rgba(1, 21, 137, 0.5)',
                    color: 'rgba(255, 255, 255, 0.5)',
                  },
                }}
                aria-label='Next Review'
              >
                <ChevronRightIcon />
              </IconButton>
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default ReviewsSection;
