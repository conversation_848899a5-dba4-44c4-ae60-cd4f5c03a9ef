import React from 'react';
import {
  AppBar,
  Toolbar,
  Button,
  Box,
  IconButton,
  Menu,
  MenuItem,
  Drawer,
  List,
  ListItem,
  ListItemText,
  Divider,
  ListItemButton,
  ListItemIcon,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import smallLogo from '@/assets/icons/general/LogoPrimary.svg';
import largeLogo from '@/assets/icons/general/Logo-CT.svg';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

const Navbar = () => {
  const router = useRouter();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const [activeMobileMenu, setActiveMobileMenu] = React.useState<null | 'klia2'>(null);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleDrawerClose = () => {
    setMobileOpen(false);
    setActiveMobileMenu(null);
  };

  const openKlia2Submenu = () => {
    setActiveMobileMenu('klia2');
  };

  const handleBackToRoot = () => {
    setActiveMobileMenu(null);
  };

  const onBookYourStayClick = () => {
    router.push('/book-your-stay');
  };

  return (
    <AppBar
      position='fixed'
      sx={{
        bgcolor: '#011589',
        boxShadow: '0 4px 10px 0 rgba(0,0,0,0.2)',
        zIndex: 1000,
        top: 0,
        left: 0,
        width: '100%',
      }}
    >
      <Toolbar
        sx={{
          maxWidth: '1440px',
          width: '100%',
          mx: 'auto',
          px: { xs: 2 },
          minHeight: { xs: '76px', md: '76px' },
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: 4,
        }}
      >
        {/* Left Section: Hamburger + Navigation */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 3,
            flex: 1,
            justifyContent: 'flex-start',
          }}
        >
          {/* Hamburger Menu */}
          <IconButton
            sx={{
              color: 'white',
              '&:hover': { opacity: 0.8 },
              p: 1,
            }}
            size='medium'
            onClick={handleDrawerToggle}
          >
            <MenuIcon />
          </IconButton>

          {/* Desktop Navigation Items */}
          <Box
            sx={{
              display: { xs: 'none', md: 'flex' },
              alignItems: 'center',
              gap: 4,
            }}
          >
            {/* KLIA Terminal 1 */}
            <Link href='/new-version/klia-1/sleep-lounge'>
              <Button
                sx={{
                  color: 'white',
                  fontWeight: 600,
                  textTransform: 'uppercase',
                  fontSize: { xs: '0.875rem', md: '14px' },
                  letterSpacing: '0.5px',
                  '&:hover': { opacity: 0.8 },
                  px: 0,
                  py: 1,
                }}
              >
                KLIA TERMINAL 1
              </Button>
            </Link>

            {/* KLIA Terminal 2 with Dropdown */}
            <Button
              sx={{
                color: 'white',
                fontWeight: 600,
                textTransform: 'uppercase',
                fontSize: { xs: '0.875rem', md: '14px' },
                letterSpacing: '0.5px',
                '&:hover': { opacity: 0.8 },
                px: 0,
                py: 1,
              }}
              endIcon={<KeyboardArrowDownIcon sx={{ fontSize: '1rem' }} />}
              onClick={handleMenu}
            >
              KLIA TERMINAL 2
            </Button>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleClose}
              sx={{ mt: 1 }}
            >
              <MenuItem
                onClick={handleClose}
                sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
              >
                <Link
                  href='/new-version/klia-2/max'
                  style={{ textDecoration: 'none', color: 'inherit' }}
                >
                  MAX
                </Link>
                <Box
                  sx={{
                    backgroundColor: '#00CED1',
                    color: 'black',
                    fontSize: '0.75rem',
                    fontWeight: 'bold',
                    padding: '2px 6px',
                    borderRadius: '4px',
                    textTransform: 'uppercase',
                    lineHeight: 1,
                  }}
                >
                  NEW
                </Box>
              </MenuItem>
              <MenuItem onClick={handleClose}>
                <Link
                  href='/new-version/klia-2/landside'
                  style={{ textDecoration: 'none', color: 'inherit' }}
                >
                  Landside
                </Link>
              </MenuItem>
              <MenuItem onClick={handleClose}>
                <Link
                  href='/new-version/klia-2/airside'
                  style={{ textDecoration: 'none', color: 'inherit' }}
                >
                  Airside
                </Link>
              </MenuItem>
            </Menu>
          </Box>
        </Box>

        {/* Center: Logo */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flex: 1,
          }}
        >
          {/* Mobile Logo */}
          <Box
            sx={{
              display: { xs: 'flex', md: 'none' },
              width: 120,
              height: 40,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Link href='/new-version' style={{ width: '100%', height: '100%' }}>
              <Image
                src={smallLogo}
                alt='CapsuleTransit Logo'
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  cursor: 'pointer',
                }}
              />
            </Link>
          </Box>

          {/* Desktop Logo */}
          <Box
            sx={{
              display: { xs: 'none', md: 'flex' },
              width: 160,
              height: 50,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Link href='/new-version' style={{ width: '100%', height: '100%' }}>
              <Image
                src={largeLogo}
                alt='CapsuleTransit Logo'
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  cursor: 'pointer',
                }}
              />
            </Link>
          </Box>
        </Box>

        {/* Right: CTA Button */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            flex: 1,
            justifyContent: 'flex-end',
          }}
        >
          {/* Desktop Button */}
          <Button
            variant='contained'
            onClick={onBookYourStayClick}
            sx={{
              display: { xs: 'none', md: 'block' },
              bgcolor: '#21FEDD',
              color: '#0F0E0E',
              fontWeight: 700,
              textTransform: 'uppercase',
              fontSize: '0.875rem',
              letterSpacing: '0.5px',
              px: 4,
              height: '40px',
              borderRadius: '4px',
              boxShadow: '0 2px 8px rgba(0, 224, 198, 0.3)',
              '&:hover': {
                bgcolor: '#21FEDD',
                boxShadow: '0 4px 12px rgba(0, 224, 198, 0.4)',
              },
            }}
          >
            BOOK YOUR STAY
          </Button>

          {/* Mobile Button */}
          <Button
            variant='contained'
            onClick={onBookYourStayClick}
            sx={{
              display: { xs: 'block', md: 'none' },
              bgcolor: '#21FEDD',
              color: '#0F0E0E',
              fontWeight: 700,
              textTransform: 'uppercase',
              fontSize: '0.875rem',
              letterSpacing: '0.5px',
              px: 3,
              height: '40px',
              borderRadius: '4px',
              boxShadow: '0 2px 8px rgba(0, 224, 198, 0.3)',
              '&:hover': {
                bgcolor: '#21FEDD',
                boxShadow: '0 4px 12px rgba(0, 224, 198, 0.4)',
              },
            }}
          >
            BOOK
          </Button>
        </Box>
      </Toolbar>

      {/* Mobile Navigation Drawer */}
      <Drawer
        variant='temporary'
        anchor='left'
        open={mobileOpen}
        onClose={handleDrawerClose}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: 320,
            bgcolor: '#011589',
            color: 'white',
            background: 'linear-gradient(135deg, #011589 0%, #0a1f5c 100%)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
          },
        }}
      >
        <Box sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* Drawer Header (always logo + close) */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              mb: 2,
              pb: 2,
              borderBottom: '1px solid rgba(119, 251, 222, 0.2)',
            }}
          >
            {/* Logo in Drawer */}
            <Box sx={{ width: 160, height: 50 }}>
              <Link href='/new-version' onClick={handleDrawerClose}>
                <Image
                  src={largeLogo}
                  alt='CapsuleTransit Logo'
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                    cursor: 'pointer',
                  }}
                />
              </Link>
            </Box>
            
            {/* Close Button */}
            <IconButton
              onClick={handleDrawerClose}
              sx={{
                color: 'white',
                bgcolor: 'rgba(119, 251, 222, 0.1)',
                borderRadius: '50%',
                width: 40,
                height: 40,
                '&:hover': { 
                  bgcolor: 'rgba(119, 251, 222, 0.2)',
                  transform: 'scale(1.05)',
                },
                transition: 'all 0.2s ease-in-out',
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>

          {/* Submenu header row (back + title) */}
          {activeMobileMenu !== null && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <IconButton
                onClick={handleBackToRoot}
                sx={{
                  color: '#77FBDE',
                  bgcolor: 'rgba(119, 251, 222, 0.1)',
                  borderRadius: '10px',
                  width: 40,
                  height: 40,
                  '&:hover': { 
                    bgcolor: 'rgba(119, 251, 222, 0.2)',
                    transform: 'translateX(-2px)'
                  },
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                <ArrowBackIosNewIcon sx={{ fontSize: '1rem' }} />
              </IconButton>
              <Box sx={{ color: '#77FBDE', fontWeight: 700, textTransform: 'uppercase', letterSpacing: '1px' }}>
                {activeMobileMenu === 'klia2' ? 'KLIA Terminal 2' : ''}
              </Box>
            </Box>
          )}

          {/* Navigation Items */}
          <Box sx={{ flex: 1, overflow: 'auto' }}>
            {activeMobileMenu === null ? (
              <List sx={{ p: 0 }}>
                {/* KLIA Terminal 1 */}
                <ListItem
                  sx={{
                    px: 0,
                    py: 0,
                    mb: 1,
                    borderRadius: '8px',
                    overflow: 'hidden',
                  }}
                >
                  <Link
                    href='/new-version/klia-1/sleep-lounge'
                    onClick={handleDrawerClose}
                    style={{ textDecoration: 'none', width: '100%' }}
                  >
                    <ListItemButton
                      sx={{
                        py: 2,
                        px: 3,
                        borderRadius: '8px',
                        bgcolor: 'rgba(119, 251, 222, 0.05)',
                        border: '1px solid rgba(119, 251, 222, 0.1)',
                        '&:hover': { 
                          bgcolor: 'rgba(119, 251, 222, 0.15)',
                          border: '1px solid rgba(119, 251, 222, 0.3)',
                          transform: 'translateX(4px)',
                        },
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      }}
                    >
                      <ListItemText
                        primary='KLIA TERMINAL 1'
                        sx={{
                          '& .MuiListItemText-primary': {
                            color: '#77FBDE',
                            fontWeight: 700,
                            textTransform: 'uppercase',
                            fontSize: '0.9rem',
                            letterSpacing: '0.8px',
                          },
                        }}
                      />
                      <ListItemIcon sx={{ minWidth: 'auto', ml: 1 }}>
                        <KeyboardArrowRightIcon sx={{ color: '#77FBDE', fontSize: '1.2rem' }} />
                      </ListItemIcon>
                    </ListItemButton>
                  </Link>
                </ListItem>

                {/* KLIA Terminal 2 (navigates to submenu view) */}
                <ListItem
                  sx={{
                    px: 0,
                    py: 0,
                    mb: 1,
                    borderRadius: '8px',
                    overflow: 'hidden',
                  }}
                >
                  <ListItemButton
                    onClick={openKlia2Submenu}
                    sx={{
                      py: 2,
                      px: 3,
                      borderRadius: '8px',
                      bgcolor: 'rgba(119, 251, 222, 0.05)',
                      border: '1px solid rgba(119, 251, 222, 0.1)',
                      '&:hover': { 
                        bgcolor: 'rgba(119, 251, 222, 0.15)',
                        border: '1px solid rgba(119, 251, 222, 0.3)',
                        transform: 'translateX(4px)',
                      },
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    }}
                  >
                    <ListItemText
                      primary='KLIA TERMINAL 2'
                      sx={{
                        '& .MuiListItemText-primary': {
                          color: '#77FBDE',
                          fontWeight: 700,
                          textTransform: 'uppercase',
                          fontSize: '0.9rem',
                          letterSpacing: '0.8px',
                        },
                      }}
                    />
                    <ListItemIcon sx={{ minWidth: 'auto', ml: 1 }}>
                      <KeyboardArrowRightIcon sx={{ color: '#77FBDE', fontSize: '1.2rem' }} />
                    </ListItemIcon>
                  </ListItemButton>
                </ListItem>
              </List>
            ) : (
              <List sx={{ p: 0 }}>
                {/* KLIA2 Submenu Items */}
                <ListItem sx={{ px: 0, py: 0, mb: 0.5 }}>
                  <Link
                    href='/new-version/klia-2/max'
                    onClick={handleDrawerClose}
                    style={{ textDecoration: 'none', width: '100%' }}
                  >
                    <ListItemButton
                      sx={{
                        py: 1.5,
                        px: 4,
                        ml: 0,
                        borderRadius: '6px',
                        bgcolor: 'rgba(255, 255, 255, 0.03)',
                        border: '1px solid rgba(255, 255, 255, 0.05)',
                        '&:hover': { 
                          bgcolor: 'rgba(119, 251, 222, 0.1)',
                          border: '1px solid rgba(119, 251, 222, 0.2)',
                          transform: 'translateX(4px)',
                        },
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      }}
                    >
                      <ListItemText
                        primary='MAX'
                        sx={{
                          '& .MuiListItemText-primary': {
                            color: 'white',
                            fontWeight: 600,
                            fontSize: '0.85rem',
                            letterSpacing: '0.5px',
                          },
                        }}
                      />
                      <Box
                        sx={{
                          backgroundColor: '#00CED1',
                          color: 'black',
                          fontSize: '0.7rem',
                          fontWeight: 'bold',
                          padding: '3px 8px',
                          borderRadius: '12px',
                          textTransform: 'uppercase',
                          lineHeight: 1,
                          ml: 1,
                        }}
                      >
                        NEW
                      </Box>
                    </ListItemButton>
                  </Link>
                </ListItem>

                <ListItem sx={{ px: 0, py: 0, mb: 0.5 }}>
                  <Link
                    href='/new-version/klia-2/landside'
                    onClick={handleDrawerClose}
                    style={{ textDecoration: 'none', width: '100%' }}
                  >
                    <ListItemButton
                      sx={{
                        py: 1.5,
                        px: 4,
                        ml: 0,
                        borderRadius: '6px',
                        bgcolor: 'rgba(255, 255, 255, 0.03)',
                        border: '1px solid rgba(255, 255, 255, 0.05)',
                        '&:hover': { 
                          bgcolor: 'rgba(119, 251, 222, 0.1)',
                          border: '1px solid rgba(119, 251, 222, 0.2)',
                          transform: 'translateX(4px)',
                        },
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      }}
                    >
                      <ListItemText
                        primary='Landside'
                        sx={{
                          '& .MuiListItemText-primary': {
                            color: 'white',
                            fontWeight: 600,
                            fontSize: '0.85rem',
                            letterSpacing: '0.5px',
                          },
                        }}
                      />
                    </ListItemButton>
                  </Link>
                </ListItem>

                <ListItem sx={{ px: 0, py: 0 }}>
                  <Link
                    href='/new-version/klia-2/airside'
                    onClick={handleDrawerClose}
                    style={{ textDecoration: 'none', width: '100%' }}
                  >
                    <ListItemButton
                      sx={{
                        py: 1.5,
                        px: 4,
                        ml: 0,
                        borderRadius: '6px',
                        bgcolor: 'rgba(255, 255, 255, 0.03)',
                        border: '1px solid rgba(255, 255, 255, 0.05)',
                        '&:hover': { 
                          bgcolor: 'rgba(119, 251, 222, 0.1)',
                          border: '1px solid rgba(119, 251, 222, 0.2)',
                          transform: 'translateX(4px)',
                        },
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      }}
                    >
                      <ListItemText
                        primary='Airside'
                        sx={{
                          '& .MuiListItemText-primary': {
                            color: 'white',
                            fontWeight: 600,
                            fontSize: '0.85rem',
                            letterSpacing: '0.5px',
                          },
                        }}
                      />
                    </ListItemButton>
                  </Link>
                </ListItem>
              </List>
            )}
          </Box>

          {/* CTA Button in Drawer */}
          <Box sx={{ mt: 3, pt: 3, borderTop: '1px solid rgba(119, 251, 222, 0.2)' }}>
            <Button
              variant='contained'
              fullWidth
              onClick={onBookYourStayClick}
              sx={{
                bgcolor: '#21FEDD',
                color: '#0F0E0E',
                fontWeight: 700,
                textTransform: 'uppercase',
                fontSize: '0.9rem',
                letterSpacing: '0.8px',
                py: 2,
                borderRadius: '8px',
                boxShadow: '0 4px 16px rgba(33, 254, 221, 0.3)',
                '&:hover': {
                  bgcolor: '#21FEDD',
                  boxShadow: '0 6px 20px rgba(33, 254, 221, 0.4)',
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              }}
            >
              BOOK YOUR STAY
            </Button>
          </Box>
        </Box>
      </Drawer>
    </AppBar>
  );
};

export default Navbar;
