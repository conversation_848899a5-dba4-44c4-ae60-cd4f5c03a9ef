'use client';

import React, { useMemo, useState } from 'react';
import {
  Box,
  Typography,
  Radio,
  RadioGroup,
  Grid,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import Image from 'next/image';

import {
  GuideContainer,
  Title,
  Instructions,
  LeftSection,
  RightSection,
  RadioOption,
} from './styled';
import { Outlet } from '@/actions/getOutlets/types';
import guideMapT2LandsideImage from '@/assets/images/guide-map/CT_AirPortMap_t2_landside.png';
import guideMapT2LandsideByBusImage from '@/assets/images/guide-map/CT_AirPortMap_t2_landside-by-bus.png';
import guideMapT2LandsideByCarImage from '@/assets/images/guide-map/CT_AirPortMap_t2_landside-by-car.png';
import guideMapT2LandsideByTrainImage from '@/assets/images/guide-map/CT_AirPortMap_t2_landside-by-train.png';
import guideMapAirsideImage from '@/assets/images/guide-map/CT_AirPortMap_airside.png';
import guideMapT1Image from '@/assets/images/guide-map/CT_AirPortMap_t1.png';
import guideMapT1ByCarImage from '@/assets/images/guide-map/CT_AirPortMap_t1-by-car.png';
import guideMapT1ByTrainImage from '@/assets/images/guide-map/CT_AirPortMap_t1-by-train.png';

// Data for the guide

const arrivalOptions = {
  landside: {
    mapUrl: guideMapT2LandsideImage,
    description:
      'Once you are in KLIA T2, depending on how you arrive, you can follow the waypoints (colour coded below) to find your way to Capsule Transit MAX on Level 2M.',
    options: [
      {
        id: 'car',
        label: 'Arriving by Car',
        color: '#E74C3C',
        bgcolor: '#323394',
        mapUrl: guideMapT2LandsideByCarImage,
      },
      {
        id: 'train',
        label: 'Arriving by Train',
        color: '#E74C3C',
        bgcolor: '#BF3A3C',
        mapUrl: guideMapT2LandsideByTrainImage,
      },
      {
        id: 'bus',
        label: 'Arriving by Bus',
        color: '#F39C12',
        bgcolor: '#EC7449',
        mapUrl: guideMapT2LandsideByBusImage,
      },
    ],
  },
  airside: {
    mapUrl: guideMapAirsideImage,
    description: '',
    options: [],
  },
  t1: {
    mapUrl: guideMapT1Image,
    description:
      'Once you are in KLIA T1, depending on how you arrive, you can follow the waypoints (colour coded below) to find your way to Capsule Transit.',
    options: [
      {
        id: 'car',
        label: 'Arriving by Car',
        color: '#E74C3C',
        bgcolor: '#323394',
        mapUrl: guideMapT1ByCarImage,
      },
      {
        id: 'train',
        label: 'Arriving by Train',
        color: '#E74C3C',
        bgcolor: '#BF3A3C',
        mapUrl: guideMapT1ByTrainImage,
      },
    ],
  },
};

const GuideKliaT2 = ({ currentOutlet }: { currentOutlet?: Outlet }) => {
  const arrivalOption = useMemo(() => {
    if (String(currentOutlet?.key).toLowerCase() === 'landside') {
      return arrivalOptions.landside;
    } else if (String(currentOutlet?.key).toLowerCase() === 'airside') {
      return arrivalOptions.airside;
    } else if (String(currentOutlet?.key).toLowerCase() === 'max') {
      return arrivalOptions.landside;
    } else if (String(currentOutlet?.key).toLowerCase() === 'sleep lounge') {
      return arrivalOptions.t1;
    }
  }, [currentOutlet?.key]);

  const [selectedOption, setSelectedOption] = useState('');
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  const handleOptionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedOption(event.target.value);
  };

  const arrivalSelectedOption = useMemo(() => {
    return (arrivalOption?.options || []).find(
      (option) => option.id === selectedOption
    );
  }, [arrivalOption?.options, selectedOption]);

  return !arrivalOption ? null : (
    <GuideContainer>
      <Grid
        container
        spacing={isMobile ? 2 : 4}
        sx={{
          maxWidth: '1108px',
          margin: '0 auto',
          width: '100% !important',
          [theme.breakpoints.down('sm')]: {
            spacing: 1,
          },
        }}
      >
        {/* Left Section */}
        <Box
          sx={{
            display: { xs: 'flex', md: 'none' },
            padding: '0 20px',
            textAlign:
              (arrivalOption.options || []).length === 0 ? 'center' : 'left',
            minHeight:
              (arrivalOption.options || []).length === 0 ? '200px' : 'auto',
            flexDirection: 'column',
            justifyContent:
              (arrivalOption.options || []).length === 0
                ? 'center'
                : 'flex-start',
          }}
        >
          <Title>How To Get There</Title>
          <Instructions>{arrivalOption.description}</Instructions>
        </Box>
        <Grid
          sx={{
            width: '100% !important',
            display: 'flex',
            flexDirection: { xs: 'column-reverse', md: 'row' },
            alignItems: 'center',
          }}
        >
          <Grid
            item
            xs={12}
            md={6}
            sx={{ paddingLeft: '0 !important', width: { xs: '100%' } }}
          >
            <LeftSection
              sx={{
                display: !isMobile
                  ? 'flex'
                  : arrivalOption.options.length > 0
                  ? 'flex'
                  : 'none',
                flexDirection: 'column',
                justifyContent:
                  (arrivalOption.options || []).length === 0
                    ? 'center'
                    : 'flex-start',
                minHeight:
                  (arrivalOption.options || []).length === 0 ? '400px' : 'auto',
              }}
            >
              <Box sx={{ display: { xs: 'none', md: 'block' } }}>
                <Title>How To Get There</Title>
                <Instructions>{arrivalOption.description}</Instructions>
              </Box>

              {(arrivalOption.options || []).length > 0 && (
                <RadioGroup
                  value={selectedOption}
                  onChange={handleOptionChange}
                  sx={{
                    transition: 'all 0.3s ease-in-out',
                    gap: '6px',
                  }}
                >
                  {(arrivalOption.options || []).map((option) => (
                    <RadioOption
                      key={option.id}
                      value={option.id}
                      bgcolor={option.bgcolor}
                      control={<Radio />}
                      sx={{
                        maxHeight: '40px',
                        borderRadius: '4px'
                      }}
                      label={
                        <Box>
                          <Typography
                            variant='body1'
                            fontWeight={500}
                            sx={{
                              fontSize: '16px',
                              color: '#FFFFFF',
                            }}
                          >
                            {option.label}
                          </Typography>
                        </Box>
                      }
                      className={
                        selectedOption === option.id ? 'Mui-checked' : ''
                      }
                    />
                  ))}
                </RadioGroup>
              )}
            </LeftSection>
          </Grid>

          {/* Right Section */}
          <Grid item xs={12} md={6}>
            <RightSection>
              <Box
                sx={{
                  position: 'relative',
                  width: '100%',
                  height: 'auto',
                  display: 'flex',
                  justifyContent: 'center',
                  [theme.breakpoints.down('sm')]: {
                    padding: theme.spacing(1),
                  },
                }}
              >
                <Image
                  src={arrivalSelectedOption?.mapUrl || arrivalOption.mapUrl}
                  alt='map'
                  width={isMobile ? 350 : isTablet ? 450 : 510}
                  height={isMobile ? 465 : isTablet ? 598 : 678}
                  style={{
                    width: '100%',
                    height: 'auto',
                    maxWidth: isMobile ? '350px' : isTablet ? '450px' : '510px',
                    borderRadius: '8px',
                  }}
                  priority
                />
              </Box>
            </RightSection>
          </Grid>
        </Grid>
      </Grid>
    </GuideContainer>
  );
};

export default GuideKliaT2;
