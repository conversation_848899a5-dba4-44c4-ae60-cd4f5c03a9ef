'use client';

import React from 'react';
import { Box, Typography, Container } from '@mui/material';
import AwardsSection from './AwardsSection';
import StatsSection from './StatsSection';
import 'swiper/css';
import 'swiper/css/free-mode';

const WelcomeSection = () => {
  return (
    <Box
      component='section'
      sx={{
        width: '100%',
        bgcolor: 'white',
        pt: { xs: 10, lg: 15 },
        pb: { xs: 10, lg: 15 },
        px: 2,
        // mt: { xs: 16, md: 16 },
      }}
    >
      <Container maxWidth='lg'>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <Typography
            variant='h2'
            sx={{
              fontSize: { xs: '1.875rem', sm: '2.25rem', md: '3rem' },
              fontWeight: 'bold',
              textAlign: 'center',
              mb: { xs: 2, md: 3 },
              color: 'text.primary',
            }}
          >
            Welcome to CapsuleTransit
          </Typography>

          <Typography
            variant='body1'
            sx={{
              fontSize: { xs: '1rem', md: '1.125rem' },
              color: 'text.secondary',
              textAlign: 'center',
              maxWidth: '2xl',
              mb: { xs: 4, md: 6 },
              lineHeight: 1.6,
              px: 1,
            }}
          >
            This is the official website of CapsuleTransit, a capsule hotel at{' '}
            <Typography
              component='span'
              sx={{
                fontWeight: 'bold',
                color: 'primary.main',
              }}
            >
              Kuala Lumpur International Airport (KLIA)
            </Typography>
            , built for travellers and flight passengers in Malaysia.
          </Typography>

          <Box
            sx={{
              maxWidth: '1000px',
              margin: '0 auto',
              width: '100%',
              mt: '60px'
            }}
          >
            {/* Stats Row */}
            <StatsSection />

            {/* Awards Section */}
            <AwardsSection />
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default WelcomeSection;
