'use client';

import React, { useRef, useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Container,
  Stack,
  IconButton,
  Paper,
  Divider,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Link from 'next/link';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';
import csrImage1 from '@/components/csr/image/img-CSR-01.jpg';
import csrImage2 from '@/components/csr/image/img-CSR-02.jpg';
import csrImage3 from '@/components/csr/image/img-CSR-03.jpg';
import csrImage4 from '@/components/csr/image/img-CSR-04.jpg';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import Image from 'next/image';

const csrImages = [csrImage1, csrImage2, csrImage3, csrImage4];

const DotButton = styled(IconButton)(({ theme }) => ({
  width: 12,
  height: 12,
  borderRadius: '50%',
  transition: 'all 0.2s',
  '&.active': {
    backgroundColor: '#223a8a',
  },
  '&:not(.active)': {
    backgroundColor: theme.palette.grey[300],
  },
}));

const CSRCampaignSection = () => {
  const swiperDesktopRef = useRef<any>(null);
  const swiperMobileRef = useRef<any>(null);
  const [imgIdx, setImgIdx] = useState(0);

  // Create slides data - each slide will contain the same content but with different images
  const slides = [
    {
      title: 'Our idea is simple.',
      content: (
        <Typography
          sx={{
            fontSize: '28px',
            fontWeight: 500,
            mb: 3,
            lineHeight: 1.2,
            color: '#0F0E0E',
          }}
        >
          <Box component='span' sx={{ color: '#4BDCC7' }}>
            RM1
          </Box>{' '}
          is donated to{' '}
          <Box
            component='span'
            sx={{ textDecoration: 'underline', fontWeight: 'bold' }}
          >
            Kechara Soup Kitchen
          </Box>{' '}
          to feed the homeless from each check-in at CapsuleTransit.
        </Typography>
      ),
      viewButtonLabel: 'View Animal Charity',
    },
    {
      title: 'Our idea is simple.',
      content: (
        <Typography
          sx={{
            fontSize: '28px',
            fontWeight: 500,
            mb: 3,
            lineHeight: 1.2,
            color: '#0F0E0E',
          }}
        >
          <Box component='span' sx={{ color: '#4BDCC7' }}>
            RM1
          </Box>{' '}
          is donated to{' '}
          <Box
            component='span'
            sx={{ textDecoration: 'underline', fontWeight: 'bold' }}
          >
            Cherish Home Life Animal Shelter
          </Box>{' '}
        </Typography>
      ),
      viewButtonLabel: 'View Human Charity',
    },
  ];

  return (
    <Box
      sx={{
        width: '100%',
        bgcolor: 'grey.50',
        background: 'linear-gradient(180deg, #F7FAFC 0%, #FFFFFF 45%)',
        py: { xs: 8, md: 10 },
        px: 2,
      }}
    >
      <Container maxWidth='xl'>
        {/* Mobile Layout */}
        <Box sx={{ display: { xs: 'block', md: 'none' } }}>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography
              variant='overline'
              sx={{
                fontWeight: 600,
                mb: 1,
                color: '#0F0E0E',
                letterSpacing: '0.1em',
                display: 'block',
                fontSize: '12px',
              }}
            >
              CSR CAMPAIGN
            </Typography>
            <Typography
              variant='h2'
              sx={{
                fontSize: '32px',
                fontWeight: 600,
                mb: 4,
                color: '#0F0E0E',
              }}
            >
              Our initiative to Give Back
            </Typography>
          </Box>
          <Box
            sx={{
              position: 'relative',
              '& .swiper-button-next, & .swiper-button-prev': {
                top: '50%',
                transform: 'translateY(-50%)',
                zIndex: 10,
                backgroundColor: 'rgba(15, 14, 14, 0.5)',
                width: '32px',
                height: '48px',
                '&:hover': {
                  backgroundColor: 'rgba(15, 14, 14, 0.8)',
                },
                boxShadow: (theme) => theme.shadows[4],
                transition: 'all 0.2s',
                borderRadius: '4px',
                '&::after': {
                  fontSize: '18px',
                  color: 'white',
                  fontWeight: 'bold',
                },
              },
              '& .swiper-button-prev': {
                left: '12px',
              },
              '& .swiper-button-next': {
                right: '12px',
              },
              '& .swiper-pagination-bullet': {
                width: 8,
                height: 8,
                backgroundColor: '#C3CAD5',
                opacity: 1,
                transition: 'all .2s ease',
              },
              '& .swiper-pagination-bullet-active': {
                backgroundColor: '#011589',
              },
            }}
          >
            <Swiper
              modules={[Navigation, Pagination]}
              navigation={false}
              pagination={false}
              spaceBetween={30}
              slidesPerView={1}
              style={{ width: '100%' }}
              onSwiper={(swiper) => {
                (swiperMobileRef as React.MutableRefObject<any>).current =
                  swiper;
              }}
            >
              {slides.map((slide, index) => (
                <SwiperSlide key={index}>
                  <Box>
                    {/* Image */}
                    <Box sx={{ mb: 6 }}>
                      <Paper
                        elevation={8}
                        sx={{
                          borderRadius: 2.5,
                          overflow: 'hidden',
                          width: '100%',
                          height: 256,
                          boxShadow: 4,
                          border: '1px solid #EEF2F6',
                          '& img': {
                            transition: 'transform .35s ease',
                          },
                          '&:hover img': {
                            transform: 'scale(1.03)',
                          },
                        }}
                      >
                        <Image
                          src={csrImages[imgIdx]}
                          alt={`CSR ${imgIdx + 1}`}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                          }}
                        />
                      </Paper>
                      {/* Dots */}
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'center',
                          gap: 1,
                          mt: 2,
                        }}
                      >
                        {csrImages.map((_, i) => (
                          <DotButton
                            key={i}
                            className={i === imgIdx ? 'active' : ''}
                            onClick={() => setImgIdx(i)}
                            aria-label={`Go to image ${i + 1}`}
                          />
                        ))}
                      </Box>
                    </Box>

                    {/* Content */}
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography
                        sx={{ fontSize: '1.125rem', mb: 2, color: 'grey.900' }}
                      >
                        {slide.title}
                      </Typography>
                      {slide.content}
                      <Box sx={{ mb: 4 }}>
                        <Typography
                          sx={{
                            color: '#0E9884',
                            fontSize: '16px',
                            mb: 1.5,
                            fontWeight: 600,
                          }}
                        >
                          And as of today, we have donated
                        </Typography>
                        <Box
                          sx={{
                            bgcolor: '#055146',
                            color: '#21FEDD',
                            fontSize: '31px',
                            fontWeight: 500,
                            px: 3,
                            borderRadius: '8px',
                            display: 'inline-block',
                            maxHeight: '54px',
                            letterSpacing: '0.4px',
                            boxShadow: (theme) => theme.shadows[2],
                          }}
                        >
                          RM 115,198
                        </Box>
                        <Button
                          sx={{
                            color: '#011589',
                            textDecoration: 'underline',
                            fontWeight: 500,
                            fontSize: '15px',
                            display: 'flex',
                            alignItems: 'center',
                            backgroundColor: 'transparent !important',
                            '&:hover': {
                              textDecoration: 'none',
                            },
                            mx: 'auto',
                          }}
                          onClick={() => {
                            const swiper = (
                              swiperMobileRef as React.MutableRefObject<any>
                            ).current;
                            if (!swiper) return;
                            if (index === 0) swiper.slideNext();
                            else swiper.slidePrev();
                          }}
                        >
                          {slide.viewButtonLabel}
                        </Button>
                      </Box>
                      <Divider
                        sx={{
                          margin: '30px 0',
                          height: '1px',
                          borderStyle: 'solid',
                          borderColor: '#D8DDE2',
                        }}
                      />
                      <Typography
                        sx={{
                          color: '#0F0E0E',
                          mb: 4,
                          fontSize: '16px',
                          lineHeight: 1.6,
                        }}
                      >
                        Join us on this journey to support others while you
                        simply get rested at KLIA!
                      </Typography>
                      <Stack spacing={2}>
                        <Link href='/book-your-stay' passHref>
                          <Button
                            variant='outlined'
                            component='a'
                            href='/book-your-stay'
                            sx={{
                              borderColor: '#011589',
                              color: '#011589',
                              fontWeight: 600,
                              px: 4,
                              borderRadius: '4px',
                              maxHeight: '40px',
                              height: '100%',
                              '&:hover': {
                                bgcolor: '#011589',
                                color: 'white',
                              },
                            }}
                          >
                            BOOK NOW
                          </Button>
                        </Link>
                        <Link href='/csr' passHref>
                          <Button
                            sx={{
                              color: '#011589',
                              textDecoration: 'underline',
                              fontWeight: 400,
                              fontSize: '15px',
                              backgroundColor: 'transparent !important',
                              '&:hover': {
                                textDecoration: 'none',
                              },
                            }}
                          >
                            Read Our CSR
                          </Button>
                        </Link>
                      </Stack>
                    </Box>
                  </Box>
                </SwiperSlide>
              ))}
            </Swiper>
            
          </Box>
        </Box>

        {/* Desktop Layout */}
        <Box
          sx={{
            display: {
              xs: 'none',
              md: 'block',
              maxWidth: '1180px',
              margin: '0 auto',
            },
          }}
        >
          <Box sx={{ mb: 4 }}>
            <Typography
              variant='overline'
              sx={{
                fontWeight: 'bold',
                mb: 1,
                color: 'grey.600',
                letterSpacing: '0.1em',
                display: 'block',
                textAlign: 'center',
              }}
            >
              CSR CAMPAIGN
            </Typography>
            <Typography
              variant='h2'
              sx={{
                fontSize: '40px',
                fontWeight: 600,
                mb: 4,
                color: '#0F0E0E',
                lineHeight: 1.2,
                textAlign: 'center',
              }}
            >
              Our Initiative to Give Back
            </Typography>
          </Box>

          <Box
            sx={{
              position: 'relative',
              '& .swiper-button-next, & .swiper-button-prev': {
                top: '50%',
                transform: 'translateY(-50%)',
                zIndex: 10,
                backgroundColor: 'rgba(15, 14, 14, 0.5)',
                width: '32px',
                height: '48px',
                '&:hover': {
                  backgroundColor: 'rgba(15, 14, 14, 0.8)',
                },
                boxShadow: (theme) => theme.shadows[4],
                transition: 'all 0.2s',
                borderRadius: '4px',
                '&::after': {
                  fontSize: '18px',
                  color: 'white',
                  fontWeight: 'bold',
                },
              },
              '& .swiper-button-prev': {
                left: '12px',
              },
              '& .swiper-button-next': {
                right: '12px',
              },
              '& .swiper-pagination-bullet': {
                width: 10,
                height: 10,
                backgroundColor: '#C3CAD5',
                opacity: 1,
                transition: 'all .2s ease',
                margin: '0 4px',
              },
              '& .swiper-pagination-bullet-active': {
                backgroundColor: '#011589',
              },
            }}
          >
            <Swiper
              modules={[Navigation, Pagination]}
              navigation={false}
              pagination={false}
              // spaceBetween={30}
              slidesPerView={1}
              style={{ width: '100%' }}
              onSwiper={(swiper) => {
                // store swiper instance for external controls
                // using any type to avoid version-specific type imports
                (swiperDesktopRef as React.MutableRefObject<any>).current =
                  swiper;
              }}
            >
              {slides.map((slide, index) => (
                <SwiperSlide key={index}>
                  <Box
                    sx={{
                      display: 'grid',
                      gridTemplateColumns: '60% 40%',
                      alignItems: 'center',
                      gap: '20px',
                      margin: '0 auto',
                    }}
                  >
                    {/* Left: Content */}
                    <Box sx={{ flex: 1, paddingLeft: '20px' }}>
                      <Box sx={{ mb: 4 }}>
                        <Typography
                          sx={{ fontSize: '20px', mb: '8px', color: '#0F0E0E' }}
                        >
                          {slide.title}
                        </Typography>
                        {slide.content}
                        <Box
                          sx={{
                            mb: 3,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 2,
                          }}
                        >
                          <Typography
                            sx={{
                              color: '#0E9884',
                              fontSize: '16px',
                              fontWeight: 600,
                            }}
                          >
                            And as of today,
                            <br /> we have donated
                          </Typography>
                          <Box
                            sx={{
                              bgcolor: '#055146',
                              color: '#21FEDD',
                              fontSize: '31px',
                              fontWeight: 500,
                              px: 2,
                              borderRadius: '8px',
                              maxHeight: '54px',
                              minWidth: '180px',
                              textAlign: 'center',
                            }}
                          >
                            RM 115,198
                          </Box>
                          <Button
                            sx={{
                              color: '#011589',
                              textDecoration: 'underline',
                              fontWeight: 500,
                              fontSize: '15px',
                              display: 'flex',
                              alignItems: 'center',
                              backgroundColor: 'transparent !important',
                              '&:hover': {
                                textDecoration: 'none',
                              },
                            }}
                            onClick={() => {
                              const swiper = (
                                swiperDesktopRef as React.MutableRefObject<any>
                              ).current;
                              if (!swiper) return;
                              if (index === 0) swiper.slideNext();
                              else swiper.slidePrev();
                            }}
                          >
                            {slide.viewButtonLabel}
                          </Button>
                        </Box>
                        <Divider
                          sx={{
                            borderColor: '#D8DDE2',
                            height: '1px',
                            borderStyle: 'solid',
                            margin: '40px 0',
                          }}
                        />
                        <Typography
                          sx={{
                            color: '#0F0E0E',
                            mb: 4,
                            fontSize: '16px',
                            lineHeight: 1.6,
                          }}
                        >
                          Join us on this journey to support others while you
                          simply get rested at KLIA!
                        </Typography>
                        <Stack direction='row' spacing={3}>
                          <Link href='/book-your-stay' passHref>
                            <Button
                              variant='outlined'
                              sx={{
                                borderColor: '#011589',
                                color: '#011589',
                                fontWeight: 600,
                                px: 2,
                                py: 1.5,
                                maxHeight: '40px',
                                borderRadius: '4px',
                                '&:hover': {
                                  bgcolor: '#011589',
                                  color: 'white',
                                },
                              }}
                            >
                              BOOK NOW
                            </Button>
                          </Link>
                          <Link href='/csr' passHref>
                            <Button
                              sx={{
                                color: '#011589',
                                textDecoration: 'underline',
                                fontWeight: 500,
                                fontSize: '15px',
                                display: 'flex',
                                alignItems: 'center',
                                backgroundColor: 'transparent !important',
                                '&:hover': {
                                  textDecoration: 'none',
                                },
                              }}
                            >
                              Read Our CSR
                            </Button>
                          </Link>
                        </Stack>
                      </Box>
                    </Box>

                    {/* Right: Image */}
                    <Box
                      sx={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        paddingRight: '20px',
                      }}
                    >
                      <Box sx={{ width: '100%', maxWidth: '350px' }}>
                        <Paper
                          elevation={12}
                          sx={{
                            borderRadius: 2.5,
                            overflow: 'hidden',
                            // width: '100%',
                            // height: { lg: 384 },
                            // boxShadow: 6,
                            width: '350px',
                            height: '350px',
                            border: '1px solid #EEF2F6',
                            '& img': {
                              transition: 'transform .35s ease',
                            },
                            '&:hover img': {
                              transform: 'scale(1.03)',
                            },
                          }}
                        >
                          <Image
                            src={csrImages[imgIdx]}
                            alt={`CSR ${imgIdx + 1}`}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                            }}
                          />
                        </Paper>
                        {/* Dots */}
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            gap: 1,
                            mt: 3,
                          }}
                        >
                          {csrImages.map((_, i) => (
                            <DotButton
                              key={i}
                              className={i === imgIdx ? 'active' : ''}
                              onClick={() => setImgIdx(i)}
                              aria-label={`Go to image ${i + 1}`}
                            />
                          ))}
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                </SwiperSlide>
              ))}
            </Swiper>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default CSRCampaignSection;
